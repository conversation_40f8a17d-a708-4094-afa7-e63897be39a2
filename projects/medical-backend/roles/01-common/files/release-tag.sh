#!/usr/bin/env bash

# ==============================================================================
#
# release-tag.sh: 一个用于在main分支上创建并推送附注标签的稳健脚本。
#
# 用法: ./release-tag.sh <tag_version>
#
# 描述:
#   此脚本自动化了版本发布的标签创建过程，并包含了一系列严格的前置检查
#   以确保发布的安全性、准确性和可重复性。
#
# 前置检查包括:
#   1. 确保提供了标签参数。
#   2. 验证当前目录是一个Git仓库。
#   3. 强制要求当前分支必须是 'main'。
#   4. 检查工作目录是否干净（无未提交的更改）。
#   5. 确保本地 'main' 分支与远程 'origin/main' 完全同步。
#   6. 检查标签是否已在本地或远程存在，防止冲突。
#
# ==============================================================================

# 严格模式: 遇到错误、未定义变量时立即退出；管道中任何命令失败则整个管道失败。
set -euo pipefail

# --- 配置 ---
# 目标发布分支
readonly MAIN_BRANCH="main"
# 远程仓库名称
readonly REMOTE_NAME="origin"

# --- 颜色定义，用于输出 ---
readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[0;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_RESET='\033[0m'

# --- 辅助函数 ---

# 错误退出函数
error_exit() {
    echo -e "${COLOR_RED}错误: $1${COLOR_RESET}" >&2
    exit 1
}

# 成功消息函数
success_msg() {
    echo -e "${COLOR_GREEN}✓ $1${COLOR_RESET}"
}

# 步骤消息函数
step_msg() {
    echo -e "${COLOR_BLUE}$1${COLOR_RESET}"
}

# 警告消息函数
warning_msg() {
    echo -e "${COLOR_YELLOW}⚠ $1${COLOR_RESET}"
}

# --- 主函数 ---
main() {
    echo -e "${COLOR_BLUE}开始Git标签发布流程...${COLOR_RESET}\n"

    # --- 1. 参数检查 ---
    step_msg "步骤 1/8: 检查命令行参数..."
    if [[ $# -lt 1 ]]; then
        error_exit "用法: $0 <tag_version> (例如: v1.2.3)"
    fi
    readonly TAG="$1"
    success_msg "将要创建的标签: $TAG"

    # --- 2. Git环境检查 ---
    step_msg "步骤 2/8: 检查Git环境..."
    if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
        error_exit "当前目录不是一个Git仓库。"
    fi
    success_msg "当前目录是一个有效的Git仓库。"

    # --- 3. 远程仓库检查 ---
    step_msg "步骤 3/8: 检查远程仓库..."
    if ! git remote get-url "$REMOTE_NAME" > /dev/null 2>&1; then
        error_exit "远程仓库 '$REMOTE_NAME' 不存在。请检查Git配置。"
    fi
    success_msg "远程仓库 '$REMOTE_NAME' 配置正确。"

    # --- 4. 分支检查 ---
    step_msg "步骤 4/8: 检查当前分支..."
    local current_branch
    current_branch=$(git rev-parse --abbrev-ref HEAD)
    if [[ "$current_branch" != "$MAIN_BRANCH" ]]; then
        error_exit "必须在 '$MAIN_BRANCH' 分支上执行此脚本。当前分支是 '$current_branch'。"
    fi
    success_msg "当前位于 '$MAIN_BRANCH' 分支。"

    # --- 5. 工作目录清洁度检查 ---
    step_msg "步骤 5/8: 检查工作目录状态..."
    if ! git diff --quiet || ! git diff --cached --quiet; then
        error_exit "工作目录不干净。请提交或储藏您的更改后再试。"
    fi
    success_msg "工作目录干净。"

    # --- 6. 远程同步检查 ---
    step_msg "步骤 6/8: 检查与远程仓库的同步状态..."
    git fetch "$REMOTE_NAME"
    local local_commit
    local remote_commit
    local_commit=$(git rev-parse HEAD)
    remote_commit=$(git rev-parse "${REMOTE_NAME}/${MAIN_BRANCH}")
    if [[ "$local_commit" != "$remote_commit" ]]; then
        error_exit "本地 '$MAIN_BRANCH' 分支与远程 '${REMOTE_NAME}/${MAIN_BRANCH}' 不同步。请先执行 'git pull'。"
    fi
    success_msg "本地 '$MAIN_BRANCH' 分支已与远程同步。"

    # --- 7. 标签冲突检查 ---
    step_msg "步骤 7/8: 检查标签冲突..."
    if git rev-parse -q --verify "refs/tags/$TAG" >/dev/null; then
        error_exit "标签 '$TAG' 已在本地存在。"
    fi
    if git ls-remote --tags "$REMOTE_NAME" | grep -q "refs/tags/$TAG$"; then
        error_exit "标签 '$TAG' 已在远程仓库 '$REMOTE_NAME' 中存在。"
    fi
    success_msg "标签 '$TAG' 未发现冲突。"

    # --- 8. 创建并推送标签 ---
    step_msg "步骤 8/8: 创建并推送附注标签..."
    local tag_message="Release version $TAG"

    # 尝试创建标签
    if ! git tag -a "$TAG" -m "$tag_message"; then
        error_exit "创建附注标签失败。请检查您的Git配置。"
    fi
    success_msg "已在本地成功创建标签 '$TAG'。"

    echo "正在将标签推送到远程仓库 '$REMOTE_NAME'..."
    if ! git push "$REMOTE_NAME" "$TAG"; then
        # 如果推送失败，清理本地创建的标签以保持状态一致
        git tag -d "$TAG" > /dev/null 2>&1
        error_exit "推送标签 '$TAG' 到远程仓库失败。已回滚本地标签创建。"
    fi

    success_msg "标签 '$TAG' 已成功推送到远程仓库。"

    echo -e "\n${COLOR_GREEN}发布流程成功完成！🎉${COLOR_RESET}"
}

# 执行主函数
main "$@"