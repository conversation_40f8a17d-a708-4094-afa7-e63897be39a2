#!/bin/bash

# Medical Backend Health Check Script
# This is an example script to demonstrate the script execution framework

set -euo pipefail

echo "===========================================" 
echo "Medical Backend Health Check"
echo "Timestamp: $(date)"
echo "Host: $(hostname)"
echo "User: $(whoami)"
echo "==========================================="

# Check system resources
echo "Checking system resources..."

# Check memory usage
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.2f", $3/$2 * 100.0}')
echo "Memory Usage: ${MEMORY_USAGE}%"

# Check disk usage
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
echo "Disk Usage: ${DISK_USAGE}%"

# Check CPU load
CPU_LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
echo "CPU Load (1min): ${CPU_LOAD}"

# Health check thresholds
MEMORY_THRESHOLD=80
DISK_THRESHOLD=85

# Perform health checks
echo "Performing health checks..."

HEALTH_STATUS="OK"

if (( $(echo "$MEMORY_USAGE > $MEMORY_THRESHOLD" | bc -l) )); then
    echo "WARNING: Memory usage (${MEMORY_USAGE}%) exceeds threshold (${MEMORY_THRESHOLD}%)"
    HEALTH_STATUS="WARNING"
fi

if (( DISK_USAGE > DISK_THRESHOLD )); then
    echo "WARNING: Disk usage (${DISK_USAGE}%) exceeds threshold (${DISK_THRESHOLD}%)"
    HEALTH_STATUS="WARNING"
fi

# # Check if medical backend services are running (example)
# echo "Checking medical backend services..."

# # Example service checks (customize based on your actual services)
# SERVICES=("docker" "nginx")

# for service in "${SERVICES[@]}"; do
#     if systemctl is-active --quiet "$service" 2>/dev/null; then
#         echo "✓ Service $service is running"
#     else
#         echo "✗ Service $service is not running or not installed"
#         # Don't fail the script for non-critical services
#     fi
# done

# Network connectivity check
echo "Checking network connectivity..."
if ping -c 1 google.com &> /dev/null; then
    echo "✓ Network connectivity: OK"
else
    echo "✗ Network connectivity: FAILED"
    HEALTH_STATUS="ERROR"
fi

# Final status
echo "==========================================="
echo "Health Check Status: $HEALTH_STATUS"
echo "Check completed at: $(date)"
echo "==========================================="

# Exit with appropriate code
if [ "$HEALTH_STATUS" = "ERROR" ]; then
    echo "Health check failed - exiting with error code 1"
    exit 1
elif [ "$HEALTH_STATUS" = "WARNING" ]; then
    echo "Health check completed with warnings - exiting with code 0 (warnings are non-fatal)"
    exit 0
else
    echo "Health check passed successfully"
    exit 0
fi 