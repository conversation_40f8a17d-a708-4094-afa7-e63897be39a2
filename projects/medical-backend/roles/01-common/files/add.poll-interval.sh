#!/bin/bash
set -euo pipefail
set -x

# 参数检查
if [ $# -ne 1 ]; then
  echo "Usage: $0"
  exit 1
fi

git_tag="$1"
project_name="neox-med-backend"

# 覆盖 base.sh 中的路径变量（以当前项目为准）
BASE_PATH="/mnt/efs/production/www"

# 加载通用函数
source "$(dirname "$0")/base.sh"

# CSV文件路径
csv_file="$(dirname "$0")/add.poll-interval.csv"

# 检查CSV文件是否存在
if [ ! -f "$csv_file" ]; then
  echo "[ERROR] CSV file not found: $csv_file"
  exit 1
fi

echo "[INFO] Starting to process CSV file: $csv_file"

# 读取CSV文件并处理每一行
line_number=0
success_count=0
error_count=0

while IFS=',' read -r merchant_id interval_ms || [[ -n "$merchant_id" ]]; do
  line_number=$((line_number + 1))

  # 跳过空行
  if [ -z "$merchant_id" ] && [ -z "$interval_ms" ]; then
    continue
  fi

  # 去除字段前后的空格和引号
  merchant_id=$(echo "$merchant_id" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//;s/^"//;s/"$//')
  interval_ms=$(echo "$interval_ms" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//;s/^"//;s/"$//')

  # 跳过标题行（如果merchant_id是"merchant_id"）
  if [ "$merchant_id" = "merchant_id" ]; then
    echo "[INFO] Skipping header line: $merchant_id, $interval_ms"
    continue
  fi

  # 验证字段是否为空
  if [ -z "$merchant_id" ] || [ -z "$interval_ms" ]; then
    echo "[WARN] Line $line_number: Empty merchant_id or interval_ms, skipping. merchant_id='$merchant_id', interval_ms='$interval_ms'"
    continue
  fi

  # 验证interval_ms是否为数字
  if ! [[ "$interval_ms" =~ ^[0-9]+$ ]]; then
    echo "[WARN] Line $line_number: interval_ms is not a valid number: '$interval_ms', skipping"
    continue
  fi

  echo "[INFO] Processing line $line_number: merchant_id=$merchant_id, interval_ms=$interval_ms"

  # 调用add_poll_interval函数
  if add_poll_interval "$project_name" "$merchant_id" "$interval_ms"; then
    success_count=$((success_count + 1))
  else
    error_count=$((error_count + 1))
    echo "[ERROR] Failed to add poll interval for merchant_id: $merchant_id"
  fi

done < <(tr -d '\r' < "$csv_file")

echo "[INFO] CSV processing completed. Total processed: $((success_count + error_count)), Success: $success_count, Errors: $error_count"

# 显示最终的轮询间隔配置
echo "[INFO] Displaying final poll interval configuration:"
list_poll_interval "$project_name"

if [ "$error_count" -gt 0 ]; then
  echo "[ERROR] Script finished with $error_count errors."
  exit 1
else
  echo "[INFO] Script finished successfully."
  exit 0
fi
