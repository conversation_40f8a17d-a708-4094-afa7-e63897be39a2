---
medical:
  home_path: "/home/<USER>"
  repo:
    ansible:
      # For Local Machine with Binary
      semaphore_home_path: "/home/<USER>"
      # The same path as tmp_path which in config.json
      repo_root_dir: "docker-compose/semaphore/tmp"
      # repo_name: ansible
  scripts:
    # Directory to store shell scripts
    relative_local_scripts_dir: "projects/medical-backend/roles/01-common/files"
    # Script execution timeout in seconds (default: -1 means no timeout)
    execution_timeout: -1
    # Base shell scripts that will be copied to the remote hosts before the release script is executed
    base_shells:
      - "base.sh"
    # CSV files without extension that will be copied to the remote hosts before the release script is executed
    relative_local_csv_file_tags:
      - "add.poll-interval"
  monitor:
    remote_root_path: "/mnt/efs/production/www/grafana/deploy"
  database:
    # MongoDB authentication update configuration
    # String replacement parameters (can be overridden via extra-vars)
    old_string: ""  # String to be replaced (must be provided via extra-vars)
    new_string: ""  # New string to replace with (must be provided via extra-vars)
    # Service configurations extracted from mongo-auth-update.md
    services:
      - service_name: "贤太API集群"
        ips: ["***********"]
        files: ["/mnt/efs/production/devops/deploy/api/.env"]
        sudo: false  # 文件操作权限
        notes: "Medical Bureau"
      - service_name: "事务局"
        ips: ["***********"]
        files:
          - "/mnt/efs/production/bureau/neox-med-backend/.env"
          - "/mnt/efs/production/bureau/bureau-backend/.env"
          - "/mnt/efs/production/bureau/neox-med-ops/.env"
          - "/mnt/efs/production/bureau/neox-ocr/.env"
        sudo: false
        notes: "Medical Bureau"
      - service_name: "贤太识别端"
        ips: ["************"]
        files: ["/mnt/efs/production/www/neox-med-backend/.env"]
        sudo: false
        notes: "Public Bastion(值班机)"
      - service_name: "Smart药局"
        ips: ["***********"]
        files: ["/mnt/efs/production/bureau/neox-smart-backend/.env"]
        sudo: false
        notes: "Medical Bureau"
      - service_name: "selector环境"
        ips: ["***********"]
        files: ["/mnt/efs/production/selector/neox-ocr/.env"]
        sudo: false
        notes: "Medical Bureau"
      - service_name: "smart药局统计执行文件相关"
        ips: ["***********"]
        files: ["/mnt/efs/production/bureau/prod.yml"]
        sudo: false
        notes: "Medical Bureau"
      - service_name: "GreenMedic"
        ips: ["**********", "***********"]
        files: ["/var/greenmedic/neox-greenmedic-backend/.env"]
        sudo: false
        notes: "Greenmedic 01;Greenmedic 02"
      # - service_name: "neox-inc官网"
      #   ips: ["*************"]
      #   files: ["/usr/share/nginx/neox-homepage-docker/.env"]
      #   sudo: true
      #   notes: "neox-inc.com"
