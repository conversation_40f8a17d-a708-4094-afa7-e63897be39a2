# Medical Backend Infrastructure Playbook

这个 Ansible 框架提供了幂等的方式来管理医疗后端服务，包括脚本执行、监控服务、数据库操作和服务重启。

## 📊 工作流程图

有关完整工作流程的可视化表示，请参见：**[工作流程图](workflow-diagram.md)**

## 功能特性

- **强大的脚本执行**: 包含错误处理、超时和适当的退出码管理
- **幂等操作**: 可安全地多次运行，结果一致
- **全面的日志记录**: 详细的执行日志，包含时间戳和状态跟踪
- **健康监控**: 内置健康检查和资源监控
- **灵活的目标定位**: 在特定清单组上执行脚本
- **备份管理**: 自动备份和清理旧的执行日志
- **服务管理**: 全面的服务重启和监控功能
- **数据库操作**: MongoDB 认证和配置管理
- **Grafana 集成**: 监控服务升级和配置更新

## 目录结构

```
projects/medical-backend/
├── ansible.cfg                              # Ansible 配置
├── site.yml                                 # 主剧本
├── README.md                                # 本文档
├── workflow-diagram.md                      # 可视化工作流程图
├── USAGE.md                                 # 使用示例和说明
├── inventory/                               # 清单配置
│   └── hosts.yml                           # 主机定义
├── extra-vars/                             # 外部变量文件
├── group_vars/                             # 组特定变量
├── keys/                                   # SSH 认证密钥
└── roles/
    ├── Dep.SetFacts/                       # 全局变量管理
    │   ├── tasks/main.yml
    │   └── vars/main.yml
    ├── Dep.SshConnection/                  # SSH 连接管理
    │   └── tasks/
    │       ├── main.yml
    │       └── task-known_hosts.yml
    ├── Dep.RepoOps/                        # 仓库操作
    │   └── tasks/
    │       ├── main.yml
    │       ├── task-git-config.yml
    │       └── task-sync-repo.yml
    ├── 01-common/                          # 主要脚本执行角色
    │   ├── meta/main.yml                   # 角色依赖
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   └── task-execute-shell-script.yml
    │   └── files/                          # 在此放置您的 shell 脚本
    │       ├── *.sh                        # 您的 shell 脚本
    │       └── base.sh                     # 基础 shell 脚本
    ├── 02-grafana/                         # 监控服务管理
    │   ├── meta/main.yml
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   ├── task-upgrade_services.yml
    │   │   └── task-update_configs.yml
    │   └── files/
    │       └── docker-compose.yml
    ├── 03-database/                        # 数据库操作
    │   └── tasks/main.yml
    └── 04-restart-services/                # 服务重启管理
        ├── tasks/
        │   ├── main.yml
        │   └── task-restart-services.yml
        └── vars/
            └── services.yml                # 服务配置
```

## 先决条件

1. 在控制机器上安装 Ansible
2. 配置对目标服务器的 SSH 访问
3. 配置包含目标服务器组的清单文件
4. 设置 SSH 密钥进行认证

## 工作流程概述

医疗后端基础设施剧本支持多种运维工作流程：

### 1. 脚本执行工作流程
- **目的**: 在远程医疗后端服务器上执行 shell 脚本
- **关键组件**: 01-common 角色、脚本验证、执行日志
- **用法**: `ansible-playbook site.yml -e "script_name=your_script.sh target_group=your_group"`

### 2. 监控服务工作流程 (Grafana)
- **目的**: 管理监控基础设施和配置
- **关键组件**: 02-grafana 角色、服务升级、配置更新
- **用法**: `ansible-playbook site.yml --tags grafana,upgrade_services`

### 3. 数据库操作工作流程
- **目的**: 处理 MongoDB 认证和配置更新
- **关键组件**: 03-database 角色、认证管理
- **用法**: `ansible-playbook site.yml --tags database`

### 4. 服务重启工作流程
- **目的**: 通过基于组的管理在多个主机上重启服务
- **关键组件**: 04-restart-services 角色、动态主机定位
- **用法**: `ansible-playbook site.yml --tags restart_services -e "restart_groups=['backend_group']"`

## 使用方法

### 📖 详细使用指南

有关全面的使用说明和示例，请参见：**[使用指南](usage.md)**

### 基本脚本执行

在特定服务器组上执行脚本：

```bash
ansible-playbook site.yml -e "script_name=your_script.sh target_group=your_group"
```

### 示例

#### 脚本执行示例
```bash
# 在所有服务器上执行健康检查
ansible-playbook site.yml -e "target_group=all script_name=health_check.sh release_tag=0 remote_scripts_dir=/mnt/efs/production/devops/deploy"

# 在后端服务器上使用 CLI 命令执行部署脚本
ansible-playbook site.yml -e "target_group=medical_servers script_name=backend.sh release_tag=v1.0.0 remote_scripts_dir=/mnt/efs/production/devops/deploy/bureau"

# 在后端服务器上使用外部变量文件执行部署脚本
ansible-playbook site.yml -e "@extra-vars/bureau.backend.yml"

# 使用自定义超时执行（默认为 -1，无超时）
ansible-playbook site.yml -e "@extra-vars/bureau.backend.yml" --extra-vars "medical.scripts.execution_timeout=600"
```

#### 监控服务 (Grafana) 示例
```bash
# 升级监控服务
ansible-playbook site.yml --tags grafana,upgrade_services

# 更新监控配置文件
ansible-playbook site.yml --tags grafana,update_configs

# 同时升级和更新配置
ansible-playbook site.yml --tags grafana
```

#### 数据库操作示例
```bash
# 执行数据库操作
ansible-playbook site.yml --tags database

# 使用自定义变量执行数据库操作
ansible-playbook site.yml --tags database -e "@extra-vars/database.yml"
```

#### 服务重启示例
```bash
# 按组重启服务
ansible-playbook site.yml --tags restart_services -e "restart_groups=['backend_group', 'frontend_group']"

# 重启特定服务
ansible-playbook site.yml --tags restart_services -e "restart_services=['service1', 'service2']"

# 混合组和服务重启
ansible-playbook site.yml --tags restart_services -e "restart_groups=['backend_group']" -e "restart_services=['specific_service']"
```

## 添加新脚本

1. 将您的 shell 脚本放置在 `roles/01-common/files/` 目录中
2. 确保脚本可执行并遵循最佳实践：
   - 包含 `#!/bin/bash` 声明
   - 使用 `set -euo pipefail` 进行错误处理
   - 提供清晰的输出和错误消息
   - 使用适当的退出码（成功为 0，失败为非零）

脚本结构示例：

```bash
#!/bin/bash
set -euo pipefail

echo "开始执行脚本..."

# 您的脚本逻辑在此处

if [ $? -eq 0 ]; then
    echo "脚本执行成功"
    exit 0
else
    echo "脚本执行失败"
    exit 1
fi
```

## 配置

### 变量

关键配置变量在 `roles/Dependencies/SetFacts/vars/main.yml` 中定义：

- `medical.scripts.execution_timeout`: 脚本执行超时时间（默认：300 秒）
- `medical.scripts.relative_local_scripts_dir`: 存储 shell 脚本的目录（相对于 medical.repo.ansible.repo_root_dir）

### 清单组

使用适当的组配置您的清单文件：

```ini
[production_servers]
bureau.prod
medical.prod

[testig_servers]
bureau.testing
medical.testing
```

或使用 yaml 文件：

```yaml
all:
  children:
    production:
      children:
        production_servers:
          hosts:
            bureau.prod:
              ansible_host: ***********
              ansible_user: ubuntu
            medical.prod:
              ansible_host: ***********
              ansible_user: ubuntu
    testing:
      children:
        testing_servers:
          hosts:
            bureau.testing:
              ansible_host: ***********
              ansible_user: ubuntu
            medical.testing:
              ansible_host: ***********
              ansible_user: ubuntu
```

## 日志记录和监控

### 执行日志

- 每次脚本执行都会在 `{remote_scripts_dir}/` 中创建详细的日志文件
- 日志文件命名格式：`{script_name}_{timestamp}.log`
- 主执行日志跟踪所有脚本运行：`execution.log`

### 日志清理

- 旧日志文件会自动清理（每个脚本保留最近 10 次执行）
- 可以通过运行清理任务手动执行清理

### 监控脚本执行

检查执行状态：

```bash
# 查看最近的执行记录
ansible all -m shell -a "tail -20 {remote_scripts_dir}/execution.log"

# 检查特定脚本日志
ansible all -m shell -a "ls -la {remote_scripts_dir} | grep your_script"
```

## 错误处理

框架包含全面的错误处理：

1. **执行前验证**: 检查脚本是否存在以及变量是否已定义
2. **超时保护**: 如果脚本超过超时时间将被终止
3. **退出码监控**: 正确检测和报告脚本失败
4. **详细错误报告**: 提供清晰的错误消息和故障排除信息
5. **日志保存**: 保留所有执行详细信息用于调试

## 最佳实践

1. **在本地测试脚本** 然后再通过 Ansible 部署
2. **使用有意义的脚本名称** 描述其用途
3. **在脚本中包含适当的错误处理**
4. **定期监控执行日志** 以发现问题
5. **保持脚本幂等性** - 可安全地多次运行
6. **为长时间运行的脚本使用适当的超时**
7. **记录脚本用途** 和预期结果

## 故障排除

### 常见问题

1. **脚本未找到**: 确保脚本存在于 `roles/01-common/files/` 中
2. **权限被拒绝**: 检查脚本权限和 SSH 访问
3. **超时错误**: 增加超时值或优化脚本性能
4. **SSH 连接问题**: 验证清单和 SSH 密钥配置

### 调试模式

使用详细输出运行以进行故障排除：

```bash
ansible-playbook site.yml -e "@extra-vars/bureau.backend.yml" -vvv
```

## 安全考虑

- 脚本使用适当的权限执行
- SSH 密钥应得到妥善保护
- 执行日志可能包含敏感信息 - 保护日志目录
- 在部署前检查脚本的安全影响
- 仅在必要时使用 sudo/become

## 贡献

添加新功能时：

1. 遵循现有的代码结构和命名约定
2. 添加适当的错误处理和日志记录
3. 更新文档和示例
4. 在开发环境中彻底测试
5. 考虑向后兼容性
