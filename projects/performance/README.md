# Performance Infrastructure

这个 Ansible 框架提供了使用 Locust 进行综合负载测试场景的性能测试基础设施的自动化部署和管理。

## 📊 工作流程图

有关完整工作流程的可视化表示，请参见：**[工作流程图](workflow-diagram.md)**

## 功能特性

- **自动化基础设施部署**: 性能测试环境的完整设置
- **Locust 集成**: 内置对 Locust 负载测试框架的支持
- **灵活配置**: 通过 extra-vars 支持多种测试场景
- **可扩展架构**: 支持使用生成器节点的分布式测试
- **环境隔离**: 独立的准备和部署阶段
- **Conda 环境管理**: 测试工具的自动化 Python 环境设置
- **三阶段部署**: 基础设施准备、生成器部署和测试执行
- **多种运行类型**: 支持启动、停止和备份操作
- **调试模式**: 用于故障排除的实时容器日志监控

## 目录结构

```
projects/performance/
├── ansible.cfg                              # Ansible 配置文件
├── site.yml                                 # 主剧本文件
├── README.md                                # 本文档
├── workflow-diagram.md                      # 可视化工作流程图
├── vault_pass.txt                          # Vault 密码文件
├── inventory/                               # 目标主机清单文件
│   └── servers.yml                         # 服务器定义
├── keys/                                    # SSH 认证密钥
├── extra-vars/                             # 外部变量配置
│   └── locust/                             # Locust 专用配置
│       ├── locust-standalone-enquete.yml   # 问卷场景配置
│       └── locust-standalone-pres.yml      # 演示场景配置
└── roles/
    ├── Dep.SetFacts/                       # 全局变量管理
    │   ├── tasks/main.yml
    │   └── vars/main.yml                   # 性能测试变量
    ├── Dep.SshConnection/                  # SSH 连接管理
    │   └── tasks/
    │       ├── main.yml
    │       └── task-known_hosts.yml
    ├── Dep.RepoOps/                        # 仓库操作
    │   └── tasks/
    │       ├── main.yml
    │       ├── task-git-config.yml
    │       └── task-sync-repo.yml
    ├── 01-prepare/                         # 基础设施准备
    │   ├── meta/main.yml                   # 角色依赖
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   ├── task-apt_update_upgrade.yml
    │   │   ├── task-install_docker_dep.yml
    │   │   └── task-install_pip3.yml
    │   └── handlers/main.yml
    ├── 02-generator/                       # 生成器服务部署
    │   ├── meta/main.yml
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   ├── task-config-generator.yml
    │   │   └── task-build-locust.yml
    │   ├── templates/
    │   │   └── auto-install-miniconda3.sh.j2
    │   └── handlers/main.yml
    └── 03-locust/                          # Locust 测试框架
        ├── tasks/
        │   ├── main.yml
        │   └── locust-standalone.yml
        └── vars/main.yml                   # Locust 配置变量
```

## 先决条件

1. 在控制机器上安装 Ansible
2. 配置对目标生成器服务器的 SSH 访问
3. 配置包含生成器组的清单文件
4. 设置 SSH 密钥进行认证
5. 具有足够资源进行负载测试的目标服务器

## 工作流程概述

性能测试基础设施遵循三阶段方法：

### 阶段 1: 基础设施准备 (01-prepare)
- **目的**: 设置基本基础设施和依赖项
- **组件**: 系统更新、Docker 安装、Python pip、Miniconda3
- **用法**: `ansible-playbook site.yml -t play_prepare`

### 阶段 2: 生成器服务部署 (02-generator)
- **目的**: 部署和配置测试生成器服务
- **组件**: Conda 环境设置、Locust Docker 镜像构建
- **用法**: `ansible-playbook site.yml -t play_generator`

### 阶段 3: Locust 性能测试 (03-locust)
- **目的**: 使用各种配置执行实际性能测试
- **组件**: 测试执行、监控、结果管理
- **运行类型**:
  - **启动**: `ansible-playbook site.yml -t locust_run -e "run_type=start"`
  - **停止**: `ansible-playbook site.yml -t locust_run -e "run_type=stop"`
  - **备份**: `ansible-playbook site.yml -t locust_run -e "run_type=backup"`

### 测试模式
- **独立模式**: 用于较小负载的单节点测试
- **分布式模式**: 多节点协调测试（计划功能）

## 使用方法

### 显示内置使用说明

剧本包含可以使用以下命令显示的全面内置使用说明：

```bash
ansible-playbook site.yml --tags usage
```

此命令将显示：
- **可用顶级标签**: `play_prepare`、`play_generator`、`locust_run`、`deploy_all`
- **Locust 任务执行详情**: 如何使用带有 `run_type` 变量的 `03-locust` 角色
- **必需变量**: `run_type`（start/stop/backup）和场景定义文件
- **可选变量**: `standalone.sync_data`、`standalone.build_image`、`standalone.debug`
- **内置示例**: 不同场景的即用命令示例

### 基础设施部署

在执行任何命令之前导航到 performance 目录：

```bash
cd projects/performance
```

#### 完整部署

部署所有性能服务（准备 + 生成器）：

```bash
# 一体化命令：部署所有组件
ansible-playbook site.yml -t deploy_all
```

#### 目标部署

单独部署特定组件：

```bash
# 仅基础设施准备
ansible-playbook site.yml -t play_prepare

# 仅生成器服务
ansible-playbook site.yml -t play_generator
```

### Locust 负载测试

#### 配置优先级

变量优先级（从高到低）：
1. 命令行指定变量（最高优先级）
2. Extra-vars 文件（`extra-vars/locust/*.yml`）
3. 角色默认变量（`roles/03-locust/vars/main.yml`）

#### 独立模式执行

使用特定配置在独立模式下执行 Locust：

```bash
# 使用特定的 extra-vars 配置文件
ansible-playbook site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"

# 替代方案：直接标签指定
ansible-playbook site.yml -t global,known_hosts,sync_repo,task_build_locust,standalone -e "@extra-vars/locust/locust-standalone-pres.yml"
```

#### 可用场景

- **问卷场景**: `extra-vars/locust/locust-standalone-enquete.yml`
- **演示场景**: `extra-vars/locust/locust-standalone-pres.yml`

### 示例

#### 完整部署示例
```bash
# 部署完整的性能测试环境
ansible-playbook site.yml -t deploy_all

# 使用特定场景配置进行部署
ansible-playbook site.yml -t deploy_all -e "@extra-vars/locust/locust-standalone-enquete.yml"
```

#### 分阶段部署示例
```bash
# 阶段 1：仅准备基础设施
ansible-playbook site.yml -t play_prepare

# 阶段 2：部署生成器服务
ansible-playbook site.yml -t play_generator

# 阶段 3：执行 Locust 测试
ansible-playbook site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"
```

#### Locust 测试示例
```bash
# 使用问卷场景启动测试
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=start"

# 使用演示场景启动测试并启用调试
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-pres.yml" -e "run_type=start" -e "standalone.debug=true"

# 启动测试并同步数据和重建镜像
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=start" -e "standalone.sync_data=true" -e "standalone.build_image=true"

# 停止正在运行的测试
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=stop"

# 备份测试结果
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-pres.yml" -e "run_type=backup"
```

#### 高级配置示例
```bash
# 从命令行覆盖默认变量
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=start" -e "standalone.scenario_data.relative_exec_scene_dir=scenarios/CustomTest/*"

# 显示使用说明
ansible-playbook site.yml --tags usage
```

## 内置使用说明详情

当您运行 `ansible-playbook site.yml --tags usage` 时，系统会显示以下综合指南：

### 可用的顶级标签
- **`play_prepare`**: 在生成器上安装所需依赖项
- **`play_generator`**: 部署和配置测试生成器服务
- **`locust_run`**: 执行 Locust 任务（启动、停止、备份）
- **`deploy_all`**: 同时运行 play_prepare 和 play_generator

### Locust 任务执行
`03-locust` 角色由 `run_type` 变量控制：

#### 必需变量
- **`run_type`**: 定义操作。可以是 'start'、'stop' 或 'backup'
- **`extra-vars`**: 您必须提供来自 `extra-vars/locust/` 的场景定义文件

#### 可选布尔变量（用于 'start' run_type）
- **`standalone.sync_data`**: （默认：false）设置为 true 以从仓库同步测试数据
- **`standalone.build_image`**: （默认：false）设置为 true 以重建 locust docker 镜像
- **`standalone.debug`**: （默认：false）设置为 true 以显示实时容器日志

#### Locust 任务的可用标签
- **`standalone`** / **`locust_standalone`**: 在独立模式下运行 locust

### 使用说明中的内置示例
```bash
# 运行所有准备和部署步骤
ansible-playbook site.yml -t deploy_all -e "@extra-vars/locust/locust-standalone-sde.yml"

# 在命令行中直接使用 extra-vars 文件启动独立 locust 测试
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml"

# 启动独立 locust 测试并启用调试
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=start" -e "standalone.debug=true"

# 停止正在运行的测试
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=stop"

# 备份测试结果
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=backup"
```

## 配置

### 变量

关键配置变量定义在：
- `roles/Dependencies/SetFacts/vars/main.yml`: 全局性能测试变量
- `roles/03-locust/vars/main.yml`: 默认 Locust 配置
- `extra-vars/locust/*.yml`: 场景特定覆盖

### 清单组

使用适当的生成器组配置您的清单文件：

```ini
[generator]
generator1.performance.local
generator2.performance.local

[locust]
locust1.performance.local
locust2.performance.local
```

### 额外变量文件

在 `extra-vars/locust/` 中创建自定义场景配置：

```yaml
# 示例：custom-scenario.yml
locust:
  target_host: "https://your-target-server.com"
  users: 100
  spawn_rate: 10
  run_time: "5m"
```

## 角色描述

### 01-prepare
- 系统更新和升级
- Docker 安装和依赖项
- Pip3 安装
- 基本基础设施准备

### 02-generator
- 生成器服务部署
- Conda 环境设置
- 密钥管理
- 服务配置

### 03-locust
- Locust 框架安装
- 测试场景配置
- 负载测试执行
- 结果收集

## 负载测试最佳实践

1. **资源规划**: 确保生成器服务器有足够的 CPU 和内存
2. **网络考虑**: 在测试期间监控网络带宽
3. **基线测试**: 从小负载开始，逐渐增加
4. **监控**: 监控生成器和目标系统指标
5. **测试持续时间**: 为有意义的结果规划适当的测试持续时间
6. **环境隔离**: 使用专用测试环境

## 监控和日志记录

### 测试执行日志

- Ansible 执行日志：`./ansible.log`
- Locust 测试结果：检查生成器服务器以获取详细结果
- 系统指标：监控 CPU、内存和网络使用情况

### 性能指标

在测试执行期间监控以下内容：
- 响应时间
- 吞吐量（每秒请求数）
- 错误率
- 目标系统上的资源利用率

## 故障排除

### 常见问题

1. **生成器连接问题**: 验证 SSH 密钥和清单配置
2. **Conda 环境错误**: 检查 Python 版本兼容性
3. **Locust 启动失败**: 验证目标主机可访问性
4. **资源约束**: 监控生成器节点上的系统资源

### 调试模式

使用详细输出运行以进行故障排除：

```bash
ansible-playbook site.yml -t deploy_all -vvv
```

### 验证命令

```bash
# 检查生成器状态
ansible generator -m ping

# 验证 Locust 安装
ansible generator -m shell -a "locust --version"

# 检查 conda 环境
ansible generator -m shell -a "conda info --envs"
```

## 安全考虑

- 保护 SSH 密钥并限制对生成器服务器的访问
- 使用隔离网络进行性能测试
- 避免在没有适当授权的情况下测试生产系统
- 监控和记录所有测试活动
- 在测试后实施适当的清理程序

## 扩展性能测试

### 分布式测试

对于大规模测试：
1. 部署多个生成器节点
2. 在分布式模式下配置 Locust
3. 使用负载均衡器进行结果聚合
4. 监控所有节点的资源使用情况

### 资源优化

- 优化测试脚本以提高效率
- 使用适当的生成速率
- 监控和调整 JVM/Python 设置
- 考虑使用专用测试硬件

## 贡献

添加新功能时：

1. 遵循现有的角色结构和命名约定
2. 为新场景添加全面的文档
3. 使用不同的负载模式进行彻底测试
4. 更新配置示例
5. 考虑与现有场景的向后兼容性

## 支持

对于问题和疑问：
1. 检查故障排除部分
2. 查看 Ansible 和 Locust 文档
3. 验证配置文件和清单
4. 监控系统日志以获取详细错误信息